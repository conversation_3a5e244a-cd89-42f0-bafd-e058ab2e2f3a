#!/usr/bin/env python3
"""
Genesis Telegram Referral Bot
Simplified version focused on core referral functionality
"""

import logging
import asyncio
import signal
import sys
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton, CopyTextButton
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes

from config import Config
from src.database import Database
from src.models.user import User
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.models.referral import Referral, ReferralStatus
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.utils.logger import setup_logger, setup_production_logging
from src.utils.security import rate_limiter, ban_manager

# Setup logging
logger = setup_logger(__name__)
startup_logger = setup_production_logging()

class GenesisBotApp:
    """Genesis Telegram Referral Bot Application"""
    
    def __init__(self):
        self.application = None
        self.database = None
        self.services = {}

        # Shutdown management
        self.shutdown_event = asyncio.Event()
        self.shutdown_in_progress = False
        
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            startup_logger.info("🤖 Starting Genesis Telegram Referral Bot...")
            startup_logger.info("🔄 Mode: Long Polling (no webhook/domain required)")
            
            # Validate configuration
            Config.validate_config()
            logger.info("✅ Configuration validated")
            
            # Initialize database connection
            startup_logger.info("🔄 Initializing database connection...")
            self.database = Database()
            db_connected = await self.database.connect()

            if db_connected:
                startup_logger.info("✅ Database connected successfully")
            else:
                startup_logger.error("❌ Database connection failed")
                raise Exception("Database connection failed")
            
            # Initialize simplified services
            startup_logger.info("🔄 Initializing simplified services...")
            self.services = {
                'user': UserService(self.database),
                'referral': ReferralService(self.database),
                'transaction': TransactionService(self.database)
            }
            startup_logger.info("✅ Services initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize async components: {e}")
            raise

    def get_main_keyboard(self):
        """Get main menu keyboard with 5-button layout"""
        keyboard = [
            [KeyboardButton("🚀 Earn Genesis Token"), KeyboardButton("💰 Genesis Token Balance")],
            [KeyboardButton("How to Earn Genesis Token❓")],
            [KeyboardButton("🏆 Top 5 Users"), KeyboardButton("ℹ️ About Genesis Bot")]
        ]
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False,
            input_field_placeholder="Choose your option! 🎁✨"
        )
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check user permissions and restrictions"""
        result = {
            'allowed': True,
            'reason': '',
            'banned': False,
            'rate_limited': False
        }
        
        # Check if user is banned
        try:
            user = await self.services['user'].get_user(user_id)
            if user and user.is_banned:
                result['allowed'] = False
                result['banned'] = True
                result['reason'] = user.ban_reason or "You are banned from using this bot."
                return result
        except:
            pass  # Continue if user service fails
        
        # Check temporary ban
        if ban_manager.is_temp_banned(user_id):
            remaining = ban_manager.get_ban_time_remaining(user_id)
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = f"You are temporarily banned. Time remaining: {remaining}"
            return result
        
        # Check rate limiting
        if rate_limiter.is_rate_limited(user_id):
            result['allowed'] = False
            result['rate_limited'] = True
            result['reason'] = "You are sending messages too quickly. Please slow down."
            return result
        
        return result
    
    def extract_referral_code(self, text: str):
        """Extract referral code from start command"""
        if not text or not text.startswith('/start'):
            return None
        
        parts = text.split()
        if len(parts) > 1:
            code = parts[1].strip()
            if len(code) == 8 and code.isalnum():
                return code.upper()
        
        return None
    
    # ==================== COMMAND HANDLERS ====================

    async def _create_user_data_structure(self, user) -> Dict[str, Any]:
        """Create user data structure from Telegram user object"""
        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'language_code': user.language_code,
            'is_bot': user.is_bot,
            'is_premium': getattr(user, 'is_premium', False)
        }

    async def _store_user_data(self, user_data: Dict[str, Any], referral_code: Optional[str]) -> Optional[User]:
        """Store user data in database with referral processing"""
        try:
            logger.debug(f"Storing user data for user {user_data['user_id']}")
            if referral_code:
                logger.debug(f"Referral code provided: {referral_code}")

            db_user = await self.services['user'].create_user(user_data, referral_code)

            if db_user:
                logger.debug(f"User data stored for user {user_data['user_id']}")
            else:
                logger.error(f"❌ FAILED: create_user returned None for user {user_data['user_id']}")

            return db_user
        except Exception as e:
            logger.error(f"❌ EXCEPTION: Failed to store user data for user {user_data['user_id']}: {e}")
            return None

    async def _handle_member_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                  user, db_user: Optional[User], referral_code: Optional[str]):
        """Handle user who is already a member of required channels"""
        # User is member of the required channel
        await self._update_user_channel_status(user.id, True)

        # Process referral after channel verification - validate immediately since user is already a member
        if referral_code and db_user and db_user.referred_by:
            await self._process_referral(db_user.user_id, db_user.referred_by, referral_code, validate_immediately=True)

        # Also validate any existing pending referrals for this user
        await self._validate_pending_referrals(user.id)

        # Show welcome
        await self._show_welcome_response(update, context, user, db_user, referral_code)

    async def _handle_non_member_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                      user, db_user: Optional[User], referral_code: Optional[str]):
        """Handle user who is not a member of required channels"""
        # User is not member of required channels
        await self._update_user_channel_status(user.id, False)

        # Create pending referral if referral code exists
        if referral_code and db_user and db_user.referred_by:
            await self._process_referral(db_user.user_id, db_user.referred_by, referral_code, validate_immediately=False)

        # Store referral code for processing after channel join (for legacy support)
        if referral_code:
            context.user_data['pending_referral'] = referral_code
            context.user_data['pending_referrer'] = db_user.referred_by if db_user else None

        # Show channel join interface
        await self._show_channel_join_interface(update, context, user)

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with enhanced referral flow - refactored for clarity"""
        try:
            user = update.effective_user

            # Check user permissions
            permissions = await self.check_user_permissions(user.id)
            if not permissions['allowed']:
                try:
                    await update.message.reply_text(permissions['reason'])
                except Exception:
                    pass
                return

            # Extract referral code from command arguments
            referral_code = None
            if context.args:
                referral_code = self.extract_referral_code(f"/start {context.args[0]}")

            # Create and store user data
            user_data = await self._create_user_data_structure(user)
            db_user = await self._store_user_data(user_data, referral_code)

            # Perform channel membership verification
            logger.debug(f"Verifying channel membership for user {user.id}")
            is_member = await self._verify_required_channels(user.id, context)

            # Handle user based on membership status
            if is_member:
                await self._handle_member_user(update, context, user, db_user, referral_code)
            else:
                await self._handle_non_member_user(update, context, user, db_user, referral_code)

        except Exception as e:
            # Handle bot blocked scenarios silently
            error_msg = str(e).lower()
            if "forbidden" in error_msg and "bot was blocked" in error_msg:
                logger.debug(f"User {user.id} has blocked the bot")
                return

            logger.error(f"Error in start command: {e}")
            try:
                await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
                """)
            except Exception:
                pass

    async def _verify_required_channels(self, user_id: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Verify if user is member of the required channel"""
        try:
            channel_id = Config.get_required_channel_id()
            logger.debug(f"Starting channel verification for user {user_id}")

            try:
                logger.info(f"Checking membership in channel: {channel_id}")
                member = await context.bot.get_chat_member(chat_id=channel_id, user_id=user_id)
                logger.info(f"User {user_id} status in channel {channel_id}: {member.status}")

                valid_statuses = ['member', 'administrator', 'creator']
                invalid_statuses = ['left', 'kicked', 'restricted']

                if member.status in invalid_statuses:
                    logger.info(f"❌ User {user_id} rejected - invalid status in channel {channel_id}: {member.status}")
                    return False

                if member.status in valid_statuses:
                    logger.info(f"✅ User {user_id} accepted - valid status in channel {channel_id}: {member.status}")
                    return True
                else:
                    logger.warning(f"⚠️ User {user_id} has unknown status in channel {channel_id}: {member.status}")
                    return False

            except Exception as e:
                error_msg = str(e).lower()
                if "chat not found" in error_msg:
                    logger.error(f"Channel {channel_id} not found - bot may not be admin or channel ID incorrect")
                elif "user not found" in error_msg:
                    logger.error(f"User {user_id} not found in channel {channel_id}")
                elif "forbidden" in error_msg:
                    logger.error(f"Bot lacks permission to check membership in channel {channel_id}")
                else:
                    logger.error(f"Unexpected error checking membership for user {user_id} in channel {channel_id}: {e}")
                return False

        except Exception as e:
            logger.error(f"Critical error in channel membership verification: {e}")
            return False

    async def _update_user_channel_status(self, user_id: int, has_joined: bool):
        """Update user's channel join status in database"""
        try:
            user = await self.services['user'].get_user(user_id)
            if user:
                user.has_joined_channels = has_joined
                await self.services['user'].update_user(user)
                logger.debug(f"Updated channel status for user {user_id}: has_joined_channels = {has_joined}")
        except Exception as e:
            logger.error(f"Failed to update channel status for user {user_id}: {e}")

    async def _safe_edit_message(self, query, text: str, parse_mode: str = None, reply_markup=None):
        """Safely edit message - handles both photo and text messages"""
        try:
            # Check if the original message has a photo (caption) or is text-only
            if query.message.photo:
                # Original message is a photo with caption - use edit_message_caption
                await query.edit_message_caption(
                    caption=text,
                    parse_mode=parse_mode,
                    reply_markup=reply_markup
                )
                logger.debug("Successfully edited photo message caption")
            else:
                # Original message is text-only - use edit_message_text
                await query.edit_message_text(
                    text=text,
                    parse_mode=parse_mode,
                    reply_markup=reply_markup
                )
                logger.debug("Successfully edited text message")

        except Exception as e:
            logger.error(f"Failed to edit message safely: {e}")
            # Fallback: try to send a new message if editing fails
            try:
                await query.message.reply_text(
                    text=text,
                    parse_mode=parse_mode,
                    reply_markup=reply_markup
                )
                logger.debug("Sent new message as fallback")
            except Exception as fallback_error:
                logger.error(f"Fallback message sending also failed: {fallback_error}")
                # Last resort: just answer the callback query
                try:
                    await query.answer("✅ Action completed")
                except:
                    pass

    async def _process_referral(self, user_id: int, referrer_id: int, referral_code: str, validate_immediately: bool = False):
        """Process referral - create pending or validated referral based on channel membership"""
        try:
            if validate_immediately:
                # User is already a channel member - create and immediately validate referral
                referral = await self.services['referral'].create_referral(referrer_id, user_id, referral_code, validate_immediately=True)
                if referral:
                    logger.info(f"✅ Referral processed and validated immediately: {referrer_id} -> {user_id}")
                else:
                    logger.error(f"❌ Failed to create validated referral: {referrer_id} -> {user_id}")
            else:
                # User is not a channel member yet - create pending referral
                referral = await self.services['referral'].create_pending_referral(referrer_id, user_id, referral_code)
                if referral:
                    logger.info(f"⏳ Pending referral created: {referrer_id} -> {user_id} (awaiting channel verification)")
                else:
                    logger.error(f"❌ Failed to create pending referral: {referrer_id} -> {user_id}")
        except Exception as e:
            logger.error(f"Error processing referral: {e}")

    async def _validate_pending_referrals(self, user_id: int):
        """Validate all pending referrals for a user who just joined the channel"""
        try:
            validated_referrals = await self.services['referral'].validate_pending_referrals(user_id)
            if validated_referrals:
                logger.info(f"✅ Validated {len(validated_referrals)} pending referrals for user {user_id}")
                return len(validated_referrals)
            return 0
        except Exception as e:
            logger.error(f"Error validating pending referrals for user {user_id}: {e}")
            return 0

    def _generate_welcome_message(self, user, db_user, referral_link: str) -> str:
        """Generate welcome message text"""
        return f"""
╔══════════════════════════════╗
║    🎉 **WELCOME TO GENESIS BOT** 🎉    ║
╚══════════════════════════════╝

👋 **Hello {user.first_name}!**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💎 **YOUR GENESIS TOKEN BALANCE**
🪙 **{int(db_user.balance) if db_user else 0}** *Genesis Tokens*

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **HOW TO EARN GENESIS TOKENS:**
💰 *Share your referral link with friends*
🎁 *Earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *per referral*
🎉 *Your friends get* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** *for joining!*

🏆 *The* **Top 5 users** *on the leaderboard each week win* **guaranteed prizes**
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Start earning now by inviting your friends!** 🚀
        """

    async def _send_welcome_message(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int,
                                    welcome_message: str, inline_reply_markup):
        """Send welcome message with photo or text fallback"""
        try:
            with open('start.jpg', 'rb') as photo_file:
                await context.bot.send_photo(
                    chat_id=chat_id,
                    photo=photo_file,
                    caption=welcome_message,
                    parse_mode='Markdown',
                    reply_markup=inline_reply_markup
                )
        except FileNotFoundError:
            await context.bot.send_message(
                chat_id=chat_id,
                text=welcome_message,
                parse_mode='Markdown',
                reply_markup=inline_reply_markup
            )

    async def _show_welcome_response(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user, db_user, referral_code):
        """Show welcome response - refactored for clarity"""
        try:
            # Generate referral link
            referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={db_user.referral_code if db_user else 'LOADING'}"

            # Generate welcome message
            welcome_message = self._generate_welcome_message(user, db_user, referral_link)

            # Create inline keyboard with copy referral link button
            copy_text_button = CopyTextButton(text=referral_link)
            inline_keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)]
            ]
            inline_reply_markup = InlineKeyboardMarkup(inline_keyboard)

            # Send welcome message with photo or text fallback
            await self._send_welcome_message(context, update.effective_chat.id, welcome_message, inline_reply_markup)

            # Send the main keyboard as a separate message
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="🏠 **Choose an option from the menu below:**",
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )

            # Mark registration as completed
            if db_user:
                await self.services['user'].mark_registration_completed(db_user.user_id)

        except Exception as e:
            logger.error(f"Error in welcome response: {e}")
            fallback_message = """
╔══════════════════════════════╗
║    🎁 **WELCOME TO GENESIS BOT** 🎉    ║
╚══════════════════════════════╝

🚀 **Start earning Genesis Tokens by inviting friends!** ✨
            """
            try:
                await update.message.reply_text(
                    fallback_message,
                    reply_markup=self.get_main_keyboard()
                )
            except:
                pass

    async def _show_channel_join_interface(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user):
        """Show channel join interface"""
        try:
            join_caption = """
╔══════════════════════════════╗
║  🔐 **CHANNEL VERIFICATION REQUIRED** 🔐  ║
╚══════════════════════════════╝

📢 *Please join our channel to use the bot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Join now to start earning Genesis Tokens!** ✨
            """
            invite_link = Config.get_channel_invite_link()

            keyboard = [
                [InlineKeyboardButton("📢 JOIN CHANNEL", url=invite_link)],
                [InlineKeyboardButton("✅ I HAVE JOINED", callback_data="verify_channels")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                with open('join.jpg', 'rb') as photo_file:
                    await context.bot.send_photo(
                        chat_id=update.effective_chat.id,
                        photo=photo_file,
                        caption=join_caption,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
            except FileNotFoundError:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=join_caption,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing channel join interface: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
            """)

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command"""
        try:
            user_id = update.effective_user.id
            telegram_user = update.effective_user

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user: {e}")
                user = None

            if not user:
                profile_text = """
👤 **Profile Not Found**

❌ *Please start the bot with /start to create your profile.*
                """
            else:
                # Format join date
                try:
                    if user.created_at and isinstance(user.created_at, datetime):
                        join_date = user.created_at.strftime("%d %b %Y")
                    else:
                        join_date = "Unknown"
                except (AttributeError, ValueError):
                    join_date = "Unknown"

                profile_text = f"""
╔══════════════════════════════╗
║  💎 **{user.get_display_name()}'s GENESIS PROFILE** 💎  ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🪙 **GENESIS TOKEN BALANCE**
💰 **{int(user.balance):,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

👥 **REFERRAL STATISTICS**
🎯 **Total Referrals:** *{user.successful_referrals} people*
💎 **Tokens Earned:** **{int(user.successful_referrals * Config.REFERRAL_REWARD):,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📅 **Member since** *{join_date}* ✨
                """

            await update.message.reply_text(
                profile_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load profile information.** ⚠️
            """)

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            help_text = f"""
╔══════════════════════════════╗
║      🎁 **GENESIS BOT GUIDE** 🎁      ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💰 **REFERRAL REWARDS**
🎯 **Per Referral:** **{Config.REFERRAL_REWARD}** *Genesis Tokens*
🎁 **Friend Bonus:** **{Config.FRIEND_WELCOME_BONUS}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **HOW TO EARN GENESIS TOKENS**
📤 *Share your referral link with friends*
👥 *Invite friends - earn* **{Config.REFERRAL_REWARD} tokens** *per join!*
📊 *Track progress with your balance*
🏆 *Compete on the leaderboard for* **prizes**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎯 **QUICK START GUIDE**
**1.** *Get your referral link from* **"Earn Genesis Token"**
**2.** *Share with friends on social media, WhatsApp, X*
**3.** *Earn* **{Config.REFERRAL_REWARD} tokens** *when friends join*
**4.** *Check leaderboard to see your ranking*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📱 **AVAILABLE FEATURES**
🪙 **Genesis Token Balance** - *Check balance & stats*
🚀 **Earn Genesis Token** - *Get referral link & stats*
🏆 **Top 5 Users** - *View leaderboard & ranking*
ℹ️ **About Genesis Bot** - *Learn about rewards*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🔗 **REFERRAL SYSTEM**
*When someone joins using your link:*
✅ *You earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *instantly*
✅ *Your friend gets* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens**
✅ *Both accounts credited automatically*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🏆 **LEADERBOARD & REWARDS**
🥇 **Top 5 users** *each week win* **guaranteed prizes**
🎉 **Weekly giveaways** *for all active users*
📈 *Climb by earning more* **Genesis Tokens**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 **PRO TIPS**
📢 *Share your link in multiple places*
👥 *Engage with friends to encourage joining*
📊 *Check balance and ranking regularly*
🎯 *Participate in* **weekly competitions**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

❓ **Need Help?**
*Genesis Bot - Your gateway to airdrops, giveaways, and* **exclusive rewards!** 🌟
            """

            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )

        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
            """)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            # Skip channel posts and group messages
            if update.channel_post or update.edited_channel_post:
                return

            if update.message and update.message.chat.type in ['group', 'supergroup']:
                return

            if not update.effective_user or not update.message or not update.message.text:
                return

            user_id = update.effective_user.id
            message_text = update.message.text

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Handle main menu buttons
            if message_text in ["Genesis Token Balance", "💰 Genesis Token Balance"]:
                await self.balance_command(update, context)
            elif message_text in ["Earn Genesis Token", "🚀 Earn Genesis Token"]:
                await self._handle_referrals_menu(update, context)
            elif message_text in ["How to Earn Genesis Token❓"]:
                await self._handle_how_to_earn(update, context)
            elif message_text in ["Top 5 Users", "🏆 Top 5 Users"]:
                await self._handle_top_users(update, context)
            elif message_text in ["About Genesis Bot", "ℹ️ About Genesis Bot"]:
                await self._handle_about_bot(update, context)
            elif message_text == "🔙 MAIN MENU":
                await update.message.reply_text(
                    """
╔══════════════════════════════╗
║    🏠 **RETURNING TO MAIN MENU** 🏠    ║
╚══════════════════════════════╝

✨ **Welcome back!** ✨
                    """,
                    reply_markup=self.get_main_keyboard(),
                    parse_mode='Markdown'
                )
            else:
                # Default message for unrecognized input
                await update.message.reply_text(
                    """
╔══════════════════════════════╗
║      🎁 **GENESIS BOT MENU** 🎁      ║
╚══════════════════════════════╝

✨ **Use the buttons below to navigate!** ✨
                    """,
                    reply_markup=self.get_main_keyboard(),
                    parse_mode='Markdown'
                )

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
            """)

    async def _handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referrals: {e}")
                user = None

            if not user:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
                referral_count = 0
                pending_count = 0
                referral_earnings = 0.0
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                    pending_count = await self.services['referral'].get_pending_referrals_count(user_id)
                except:
                    referral_count = user.successful_referrals
                    pending_count = 0

                referral_earnings = referral_count * Config.REFERRAL_REWARD

            # Show pending referrals info if any exist
            pending_info = ""
            if pending_count > 0:
                pending_info = f"""
⏳ **Pending Referrals:** **{pending_count}** *awaiting channel verification*
"""

            referral_text = f"""
╔══════════════════════════════╗
║    🪙 **EARN GENESIS TOKENS** 🪙    ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📊 **YOUR REFERRAL PERFORMANCE**
✅ **Validated Referrals:** **{referral_count}** *people*{pending_info}
💰 **Total Earned:** **{int(referral_earnings):,}** *Genesis Tokens*
🎯 **Per Referral:** **{Config.REFERRAL_REWARD}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 **HOW TO EARN GENESIS TOKENS**
**1.** *Share your link with friends*
**2.** *They join using your link*
**3.** *They must join our update channel*
**4.** *You earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *per validated referral!*
**5.** *Your friend gets* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** *for joining!*

⚠️ **Note:** *Referrals are validated only after users join the required channel*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **SHARE YOUR LINK ON:**
📱 *WhatsApp, Telegram groups*
🌐 *Social media platforms*
👥 *With friends and family*
            """

            # Create copy referral link button with native clipboard copying
            copy_text_button = CopyTextButton(text=referral_link)
            keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)],
                [InlineKeyboardButton("📊 Referral Stats", callback_data="referral_stats")],
                [InlineKeyboardButton("🔙 Main Menu", callback_data="main_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load referral information.** ⚠️
            """)

    async def _handle_top_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle Top 5 Users leaderboard with pagination support"""
        await self._show_leaderboard(update, context, page=0)

    async def _show_leaderboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                               page: int = 0, sort_by: str = 'balance'):
        """Show paginated leaderboard"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get leaderboard data with pagination
            per_page = 10
            try:
                top_users = await self.services['user'].get_leaderboard(page=page, per_page=per_page, sort_by=sort_by)
                total_pages = await self.services['user'].get_leaderboard_total_pages(per_page=per_page, sort_by=sort_by)

                # Get current user's rank and data
                current_user = await self.services['user'].get_user(user_id)
                if current_user:
                    # Count users with higher balance
                    higher_balance_count = await self.database.db.users.count_documents(
                        {"balance": {"$gt": current_user.balance}, "is_banned": False, "is_active": True}
                    )
                    current_user_rank = higher_balance_count + 1
                else:
                    current_user_rank = "N/A"

            except Exception as e:
                logger.error(f"Failed to get leaderboard data: {e}")
                await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load leaderboard.** ⚠️
                """)
                return

            # Format leaderboard message
            sort_title = {
                'balance': 'GENESIS TOKEN HOLDERS',
                'referral_count': 'REFERRAL LEADERS',
                'total_earned': 'TOP EARNERS',
                'successful_referrals': 'REFERRAL CHAMPIONS'
            }.get(sort_by, 'LEADERBOARD')

            leaderboard_text = f"""
╔══════════════════════════════╗
║  🏆 **{sort_title}** 🏆  ║
╚══════════════════════════════╝

📄 **Page {page + 1} of {total_pages}**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

"""

            if top_users:
                for i, user in enumerate(top_users, 1):
                    rank = (page * per_page) + i
                    first_name = user.first_name or 'Anonymous'

                    if sort_by == 'balance':
                        value = f"{int(user.balance):,} Genesis Tokens"
                    elif sort_by == 'referral_count':
                        value = f"{user.referral_count} referrals"
                    elif sort_by == 'total_earned':
                        value = f"{int(user.total_earned):,} tokens earned"
                    else:
                        value = f"{user.successful_referrals} successful"

                    # Add medal emojis for top 3 on first page
                    if page == 0 and rank <= 3:
                        medals = ["🥇", "🥈", "🥉"]
                        medal = medals[rank - 1]
                        rank_style = "**"
                    else:
                        medal = f"🔸 {rank}."
                        rank_style = ""

                    leaderboard_text += f"{medal} {rank_style}{first_name}{rank_style} - **{value}**\n"
            else:
                leaderboard_text += "🎯 **No users found for this page.**\n"

            leaderboard_text += f"""
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📍 **YOUR POSITION**
🏆 **Rank:** **#{current_user_rank}**
💰 **Balance:** **{int(current_user.balance) if current_user else 0:,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 *Climb the leaderboard by earning more* **Genesis Tokens!**
🚀 *Share your referral link to earn* **{Config.REFERRAL_REWARD} tokens** *per friend.*
            """

            # Create navigation buttons
            keyboard = []
            nav_row = []

            # Previous page button
            if page > 0:
                nav_row.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"leaderboard_page_{page-1}_{sort_by}"))

            # Page indicator
            nav_row.append(InlineKeyboardButton(f"📄 {page + 1}/{total_pages}", callback_data="noop"))

            # Next page button
            if page < total_pages - 1:
                nav_row.append(InlineKeyboardButton("Next ➡️", callback_data=f"leaderboard_page_{page+1}_{sort_by}"))

            if nav_row:
                keyboard.append(nav_row)

            # Sort options
            sort_row = []
            if sort_by != 'balance':
                sort_row.append(InlineKeyboardButton("💰 Balance", callback_data=f"leaderboard_page_0_balance"))
            if sort_by != 'referral_count':
                sort_row.append(InlineKeyboardButton("👥 Referrals", callback_data=f"leaderboard_page_0_referral_count"))

            if sort_row:
                keyboard.append(sort_row)

            reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

            await update.message.reply_text(
                leaderboard_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in top users handler: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load leaderboard.** ⚠️
            """)

    async def _handle_about_bot(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle About Genesis Bot information"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            about_text = """
╔══════════════════════════════╗
║  🚀 **GENESIS WHALES BOT** 🐋  ║
╚══════════════════════════════╝

🌟 **Powered by Genesis Crypto Whales**

*Your gateway to airdrops, giveaways, and* **exclusive rewards** *starts here.*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎁 **WHAT IS IT?**

*Genesis Whales Bot is an* **official rewards system** *created to distribute airdrops, gifts, and weekly prizes to our community.*

✅ **Invite friends**
✅ **Earn Genesis Tokens**
✅ **Climb the leaderboard**
✅ **Win guaranteed rewards**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🪙 **HOW IT WORKS**

💰 *Every friend you refer =* **50 Genesis Tokens** *for you*
🎁 *Your friend also gets* **25 Genesis Tokens** *for joining with your link!*

🏆 *The* **Top 5 users** *on the leaderboard each week win* **guaranteed prizes**
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📝 **STEPS TO START EARNING**
**1.** *Tap* **"Earn Genesis Tokens"** *in the bot*
**2.** *Copy your unique referral link*
**3.** *Share it with your network (Telegram, WhatsApp, X, anywhere)*
**4.** *Earn tokens as your friends join through your link*
**5.** *Track your rank in the* **Leaderboard** *tab*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎯 **The more you share, the more you earn.**
💎 *Start stacking* **Genesis Tokens** *and climb your way to* **exclusive drops!**

👉 **Ready to begin?** ✨
            """

            await update.message.reply_text(
                about_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in about bot handler: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load information.** ⚠️
            """)

    async def _handle_how_to_earn(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle How to Earn Genesis Token information"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get user data for referral link
            try:
                user = await self.services['user'].get_user(user_id)
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code if user else 'LOADING'}"
            except Exception as e:
                logger.error(f"Failed to get user for how to earn: {e}")
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"

            how_to_earn_text = """
🚀 **HOW TO EARN GENESIS TOKENS:**
💰 *Share your referral link with friends*
🎁 *Earn* **50 Genesis Tokens** *per referral*
🎉 *Your friends get* **25 Genesis Tokens** *for joining!*

🏆 *The* **Top 5 users** *on the leaderboard each week win guaranteed prizes*
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Start earning now by inviting your friends!** 🚀
            """

            # Create inline keyboard with copy referral link button
            copy_text_button = CopyTextButton(text=referral_link)
            keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                how_to_earn_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in how to earn handler: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load information.** ⚠️
            """)

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries"""
        try:
            query = update.callback_query
            await query.answer()

            user_id = query.from_user.id
            data = query.data

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await self._safe_edit_message(query, permissions['reason'])
                return

            if data == "verify_channels":
                await self._handle_channel_verification_callback(query, context)
            elif data == "referral_stats":
                await self._show_referral_stats(query, context)
            elif data.startswith("leaderboard_page_"):
                await self._handle_leaderboard_pagination(query, context)
            elif data == "main_menu":
                main_menu_text = """
╔══════════════════════════════╗
║        🏠 **MAIN MENU** 🏠        ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎁 **Use the buttons below to navigate!** ✨
                """
                await self._safe_edit_message(query, main_menu_text, parse_mode='Markdown')
            elif data == "noop":
                # No operation - just acknowledge the callback
                pass
            else:
                await self._safe_edit_message(query, "❌ Unknown action.")

        except Exception as e:
            logger.error(f"Error in callback handler: {e}")
            try:
                await query.answer("❌ An error occurred.")
            except:
                pass

    async def _handle_leaderboard_pagination(self, query, context):
        """Handle leaderboard pagination callbacks"""
        try:
            # Parse callback data: leaderboard_page_{page}_{sort_by}
            parts = query.data.split('_')
            if len(parts) >= 4:
                page = int(parts[2])
                sort_by = parts[3]
            else:
                page = 0
                sort_by = 'balance'

            # Create a fake update object for the leaderboard method
            fake_update = type('obj', (object,), {
                'effective_user': query.from_user,
                'message': query.message
            })

            # Show the requested leaderboard page
            await self._show_leaderboard_callback(query, context, page=page, sort_by=sort_by)

        except Exception as e:
            logger.error(f"Error handling leaderboard pagination: {e}")
            await self._safe_edit_message(query, "❌ Failed to load leaderboard page.")

    async def _show_leaderboard_callback(self, query, context, page: int = 0, sort_by: str = 'balance'):
        """Show paginated leaderboard for callback queries"""
        try:
            user_id = query.from_user.id

            # Get leaderboard data with pagination
            per_page = 10
            try:
                top_users = await self.services['user'].get_leaderboard(page=page, per_page=per_page, sort_by=sort_by)
                total_pages = await self.services['user'].get_leaderboard_total_pages(per_page=per_page, sort_by=sort_by)

                # Get current user's rank and data
                current_user = await self.services['user'].get_user(user_id)
                if current_user:
                    # Count users with higher balance
                    higher_balance_count = await self.database.db.users.count_documents(
                        {"balance": {"$gt": current_user.balance}, "is_banned": False, "is_active": True}
                    )
                    current_user_rank = higher_balance_count + 1
                else:
                    current_user_rank = "N/A"

            except Exception as e:
                logger.error(f"Failed to get leaderboard data: {e}")
                await self._safe_edit_message(query, """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load leaderboard.** ⚠️
                """)
                return

            # Format leaderboard message (same as _show_leaderboard but for callbacks)
            sort_title = {
                'balance': 'GENESIS TOKEN HOLDERS',
                'referral_count': 'REFERRAL LEADERS',
                'total_earned': 'TOP EARNERS',
                'successful_referrals': 'REFERRAL CHAMPIONS'
            }.get(sort_by, 'LEADERBOARD')

            leaderboard_text = f"""
╔══════════════════════════════╗
║  🏆 **{sort_title}** 🏆  ║
╚══════════════════════════════╝

📄 **Page {page + 1} of {total_pages}**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

"""

            if top_users:
                for i, user in enumerate(top_users, 1):
                    rank = (page * per_page) + i
                    first_name = user.first_name or 'Anonymous'

                    if sort_by == 'balance':
                        value = f"{int(user.balance):,} Genesis Tokens"
                    elif sort_by == 'referral_count':
                        value = f"{user.referral_count} referrals"
                    elif sort_by == 'total_earned':
                        value = f"{int(user.total_earned):,} tokens earned"
                    else:
                        value = f"{user.successful_referrals} successful"

                    # Add medal emojis for top 3 on first page
                    if page == 0 and rank <= 3:
                        medals = ["🥇", "🥈", "🥉"]
                        medal = medals[rank - 1]
                        rank_style = "**"
                    else:
                        medal = f"🔸 {rank}."
                        rank_style = ""

                    leaderboard_text += f"{medal} {rank_style}{first_name}{rank_style} - **{value}**\n"
            else:
                leaderboard_text += "🎯 **No users found for this page.**\n"

            leaderboard_text += f"""
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📍 **YOUR POSITION**
🏆 **Rank:** **#{current_user_rank}**
💰 **Balance:** **{int(current_user.balance) if current_user else 0:,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 *Climb the leaderboard by earning more* **Genesis Tokens!**
🚀 *Share your referral link to earn* **{Config.REFERRAL_REWARD} tokens** *per friend.*
            """

            # Create navigation buttons
            keyboard = []
            nav_row = []

            # Previous page button
            if page > 0:
                nav_row.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"leaderboard_page_{page-1}_{sort_by}"))

            # Page indicator
            nav_row.append(InlineKeyboardButton(f"📄 {page + 1}/{total_pages}", callback_data="noop"))

            # Next page button
            if page < total_pages - 1:
                nav_row.append(InlineKeyboardButton("Next ➡️", callback_data=f"leaderboard_page_{page+1}_{sort_by}"))

            if nav_row:
                keyboard.append(nav_row)

            # Sort options
            sort_row = []
            if sort_by != 'balance':
                sort_row.append(InlineKeyboardButton("💰 Balance", callback_data=f"leaderboard_page_0_balance"))
            if sort_by != 'referral_count':
                sort_row.append(InlineKeyboardButton("👥 Referrals", callback_data=f"leaderboard_page_0_referral_count"))

            if sort_row:
                keyboard.append(sort_row)

            reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

            await self._safe_edit_message(query, leaderboard_text, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            logger.error(f"Error showing leaderboard callback: {e}")
            await self._safe_edit_message(query, "❌ Failed to load leaderboard.")

    async def _handle_channel_verification_callback(self, query, context):
        """Handle channel verification callback"""
        try:
            user_id = query.from_user.id

            # Verify channel membership
            is_member = await self._verify_required_channels(user_id, context)

            if is_member:
                # Update user status
                await self._update_user_channel_status(user_id, True)

                # Validate all pending referrals for this user
                validated_count = await self._validate_pending_referrals(user_id)

                # Process legacy pending referral if any (for backward compatibility)
                if 'pending_referral' in context.user_data and 'pending_referrer' in context.user_data:
                    referral_code = context.user_data['pending_referral']
                    referrer_id = context.user_data['pending_referrer']

                    if referrer_id:
                        await self._process_referral(user_id, referrer_id, referral_code, validate_immediately=True)

                    # Clear pending data
                    del context.user_data['pending_referral']
                    del context.user_data['pending_referrer']

                # Get user data for welcome
                user = await self.services['user'].get_user(user_id)
                telegram_user = query.from_user

                # Delete the channel join message
                try:
                    await query.message.delete()
                    logger.debug("Successfully deleted channel join message")
                except Exception as e:
                    logger.error(f"Failed to delete channel join message: {e}")

                # Generate referral link
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code if user else 'LOADING'}"

                # Send the complete welcome message (same as /start command)
                welcome_message = f"""
╔══════════════════════════════╗
║    🎉 **WELCOME TO GENESIS BOT** 🎉    ║
╚══════════════════════════════╝

👋 **Hello {telegram_user.first_name}!**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💎 **YOUR GENESIS TOKEN BALANCE**
🪙 **{int(user.balance) if user else 0}** *Genesis Tokens*

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **HOW TO EARN GENESIS TOKENS:**
💰 *Share your referral link with friends*
🎁 *Earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *per referral*
🎉 *Your friends get* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** *for joining!*

🏆 *The* **Top 5 users** *on the leaderboard each week win* **guaranteed prizes**
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Start earning now by inviting your friends!** 🚀
                """

                # Create inline keyboard with copy referral link button
                copy_text_button = CopyTextButton(text=referral_link)
                inline_keyboard = [
                    [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)]
                ]
                inline_reply_markup = InlineKeyboardMarkup(inline_keyboard)

                # Send the welcome message with copy referral link button
                try:
                    with open('start.jpg', 'rb') as photo_file:
                        await context.bot.send_photo(
                            chat_id=query.message.chat_id,
                            photo=photo_file,
                            caption=welcome_message,
                            parse_mode='Markdown',
                            reply_markup=inline_reply_markup
                        )
                except FileNotFoundError:
                    await context.bot.send_message(
                        chat_id=query.message.chat_id,
                        text=welcome_message,
                        parse_mode='Markdown',
                        reply_markup=inline_reply_markup
                    )

                # Send the main keyboard as a separate message
                await context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text="🏠 **Choose an option from the menu below:**",
                    parse_mode='Markdown',
                    reply_markup=self.get_main_keyboard()
                )

                # Mark registration as completed
                if user:
                    await self.services['user'].mark_registration_completed(user.user_id)

            else:
                failure_message = """
╔══════════════════════════════╗
║    ❌ **VERIFICATION FAILED** ❌    ║
╚══════════════════════════════╝

⚠️ **Please make sure you have joined the channel and try again.** ⚠️
                """

                # Use appropriate edit method based on message type
                await self._safe_edit_message(
                    query,
                    failure_message,
                    parse_mode='Markdown',
                    reply_markup=query.message.reply_markup
                )

        except Exception as e:
            logger.error(f"Error in channel verification callback: {e}")
            error_message = """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Verification failed. Please try again.** ⚠️
            """
            # Use appropriate edit method based on message type
            await self._safe_edit_message(query, error_message)



    async def _show_referral_stats(self, query, context):
        """Show detailed referral statistics"""
        try:
            user_id = query.from_user.id
            user = await self.services['user'].get_user(user_id)

            if user:
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                    pending_count = await self.services['referral'].get_pending_referrals_count(user_id)
                except:
                    referral_count = user.successful_referrals
                    pending_count = 0

                total_earned = referral_count * Config.REFERRAL_REWARD
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"

                # Show pending referrals info if any exist
                pending_info = ""
                if pending_count > 0:
                    pending_info = f"""
⏳ **Pending Referrals:** **{pending_count}** *awaiting channel verification*
"""

                stats_text = f"""
╔══════════════════════════════╗
║  📊 **DETAILED REFERRAL STATS** 📊  ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📈 **YOUR PERFORMANCE**
✅ **Validated Referrals:** **{referral_count}** *people*{pending_info}
💰 **Total Earned:** **{int(total_earned):,}** *Genesis Tokens*
🎯 **Per Referral:** **{Config.REFERRAL_REWARD}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 **Note:** *Referrals are validated when users join our update channel*

🚀 **Keep sharing to earn more Genesis Tokens!** ✨
                """

                keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="main_menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await self._safe_edit_message(
                    query,
                    stats_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                error_text = """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load referral statistics.** ⚠️
                """
                await self._safe_edit_message(query, error_text)

        except Exception as e:
            logger.error(f"Error showing referral stats: {e}")
            error_text = """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load statistics.** ⚠️
            """
            await self._safe_edit_message(query, error_text)

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors"""
        logger.error(f"Exception while handling an update: {context.error}")

    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("🔄 Shutting down bot...")
        self.shutdown_in_progress = True
        self.shutdown_event.set()

        if self.database:
            await self.database.close()

        logger.info("✅ Bot shutdown complete")

# ==================== MAIN FUNCTION ====================

async def main():
    """Main function to run the bot"""
    bot = GenesisBotApp()

    try:
        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        bot.application = application

        # Initialize async components
        await bot.initialize_async_components()

        # Add handlers
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("balance", bot.balance_command))
        application.add_handler(CommandHandler("help", bot.help_command))

        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND & filters.ChatType.PRIVATE, bot.handle_message))
        application.add_handler(CallbackQueryHandler(bot.handle_callback))
        application.add_error_handler(bot.error_handler)

        startup_logger.info("✅ All handlers added successfully")

        # Start the bot manually to avoid event loop issues
        startup_logger.info("🚀 Starting bot in long polling mode...")

        # Initialize and start the application manually
        await application.initialize()
        await application.start()

        # Start the updater
        await application.updater.start_polling(drop_pending_updates=True)

        # Keep the bot running
        try:
            # Create a simple event to keep the bot running
            stop_event = asyncio.Event()

            # Set up signal handlers
            import signal
            def signal_handler(signum, frame):
                logger.info(f"Received signal {signum}, stopping bot...")
                stop_event.set()

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # Wait for stop signal
            await stop_event.wait()

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, stopping bot...")
        finally:
            # Clean shutdown
            await application.updater.stop()
            await application.stop()
            await application.shutdown()

    except Exception as e:
        logger.error(f"❌ Critical error in main: {e}")
        raise

if __name__ == '__main__':
    try:
        # Use asyncio.run() to handle the event loop properly
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
