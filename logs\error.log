2025-07-30 21:58:30 - __main__ - ERROR - main:992 - ❌ Critical error in main: Cannot close a running event loop
2025-07-30 21:58:30 - __main__ - ERROR - <module>:1003 - Fatal error: Cannot close a running event loop
2025-07-30 21:59:24 - __main__ - ERROR - main:984 - ❌ Critical error in main: Cannot close a running event loop
2025-07-30 21:59:24 - __main__ - ERROR - <module>:996 - Fatal error: Cannot close a running event loop
2025-07-30 22:00:11 - __main__ - ERROR - main:984 - ❌ Critical error in main: Cannot close a running event loop
2025-07-30 22:00:11 - __main__ - ERROR - <module>:993 - Fatal error: Cannot close a running event loop
2025-07-30 22:00:53 - __main__ - ERROR - main:991 - ❌ Critical error in main: 'Updater' object has no attribute 'idle'
2025-07-30 22:02:18 - __main__ - ERROR - main:990 - ❌ Critical error in main: There is no current event loop in thread 'MainThread'.
2025-07-30 22:02:18 - __main__ - ERROR - <module>:999 - Fatal error: There is no current event loop in thread 'MainThread'.
2025-07-30 22:03:27 - __main__ - ERROR - main:984 - ❌ Critical error in main: Cannot close a running event loop
2025-07-30 22:03:27 - __main__ - ERROR - <module>:993 - Fatal error: Cannot close a running event loop
2025-07-30 22:05:43 - __main__ - ERROR - main:984 - ❌ Critical error in main: Cannot close a running event loop
2025-07-30 22:05:43 - __main__ - ERROR - <module>:993 - Fatal error: Cannot close a running event loop
2025-07-30 22:10:05 - __main__ - ERROR - main:991 - ❌ Critical error in main: There is no current event loop in thread 'MainThread'.
2025-07-30 22:10:05 - __main__ - ERROR - <module>:1000 - Fatal error: There is no current event loop in thread 'MainThread'.
2025-07-30 22:11:12 - __main__ - ERROR - main:999 - ❌ Critical error in main: 'Updater' object has no attribute 'idle'
2025-07-30 22:11:13 - __main__ - ERROR - <module>:1015 - Fatal error: 'Updater' object has no attribute 'idle'
2025-07-30 22:12:34 - __main__ - ERROR - <module>:994 - Fatal error: Cannot close a running event loop
2025-07-30 22:20:40 - __main__ - ERROR - main:984 - ❌ Critical error in main: Cannot close a running event loop
2025-07-30 22:20:40 - __main__ - ERROR - <module>:994 - Fatal error: Cannot close a running event loop
2025-07-30 22:23:58 - __main__ - ERROR - _verify_required_channels:256 - Channel -1001296547211 not found - bot may not be admin or channel ID incorrect
2025-07-30 22:38:48 - __main__ - ERROR - _verify_required_channels:256 - Channel -1002802011632 not found - bot may not be admin or channel ID incorrect
2025-07-30 23:10:36 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:10:36 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-30 23:10:43 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:10:44 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-30 23:10:47 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:10:47 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-30 23:10:56 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:10:57 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-30 23:11:06 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:11:06 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-30 23:11:08 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:11:09 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-30 23:11:10 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:11:10 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-30 23:11:11 - __main__ - ERROR - _handle_channel_verification_callback:1017 - Error in channel verification callback: There is no text in the message to edit
2025-07-30 23:11:11 - __main__ - ERROR - handle_callback:944 - Error in callback handler: There is no text in the message to edit
2025-07-31 00:06:14 - __main__ - ERROR - _handle_how_to_earn:999 - Error in how to earn handler: InlineKeyboardButton.__init__() got an unexpected keyword argument 'copy_text'
2025-07-31 00:20:05 - __main__ - ERROR - _handle_how_to_earn:999 - Error in how to earn handler: InlineKeyboardButton.__init__() got an unexpected keyword argument 'copy_text'
2025-07-31 00:20:18 - __main__ - ERROR - _handle_how_to_earn:999 - Error in how to earn handler: InlineKeyboardButton.__init__() got an unexpected keyword argument 'copy_text'
2025-07-31 00:22:24 - final_bot - ERROR - _show_welcome_response:401 - Error in welcome response: 'user'
2025-07-31 00:32:48 - __main__ - ERROR - _handle_how_to_earn:999 - Error in how to earn handler: Can't parse inline keyboard button: field "copy_text" must be of type object
2025-07-31 00:32:56 - __main__ - ERROR - _handle_how_to_earn:999 - Error in how to earn handler: Can't parse inline keyboard button: field "copy_text" must be of type object
