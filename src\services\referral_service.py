"""
Referral service for managing referral operations
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any

from src.database import Database
from src.models.referral import Referral, ReferralStatus
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.utils.logger import log_transaction
from config import Config

logger = logging.getLogger(__name__)

class ReferralService:
    """Service for referral management operations"""

    def __init__(self, database: Database):
        self.db = database
    
    async def create_referral(self, referrer_id: int, referred_id: int, referral_code: str, validate_immediately: bool = False) -> Optional[Referral]:
        """Create a new referral with optional immediate validation"""
        try:
            # Check if referral already exists
            existing = await self.db.referrals.find_one({
                'referrer_id': referrer_id,
                'referred_id': referred_id
            })

            if existing:
                return Referral.from_dict(existing)

            # Use static referral reward amount from config
            reward_amount = Config.REFERRAL_REWARD

            # Create referral - starts as PENDING by default
            referral = Referral(
                referrer_id=referrer_id,
                referred_id=referred_id,
                referral_code=referral_code,
                reward_amount=reward_amount,
                status=ReferralStatus.PENDING  # Always start as pending
            )

            # Save to database
            await self.db.referrals.insert_one(referral.to_dict())
            logger.info(f"Created pending referral: {referrer_id} -> {referred_id}")

            # If immediate validation is requested (for channel members), complete it
            if validate_immediately:
                await self.complete_referral(referral.referral_id)
                logger.info(f"Immediately validated referral: {referrer_id} -> {referred_id}")

            return referral

        except Exception as e:
            logger.error(f"Failed to create referral {referrer_id} -> {referred_id}: {e}")
            return None

    async def create_pending_referral(self, referrer_id: int, referred_id: int, referral_code: str) -> Optional[Referral]:
        """Create a pending referral that requires channel validation"""
        return await self.create_referral(referrer_id, referred_id, referral_code, validate_immediately=False)

    async def validate_pending_referrals(self, user_id: int) -> List[str]:
        """Validate all pending referrals for a user who just joined the channel"""
        try:
            # Find all pending referrals where this user was referred
            pending_referrals = await self.db.referrals.find({
                'referred_id': user_id,
                'status': ReferralStatus.PENDING.value
            }).to_list(None)

            validated_referral_ids = []

            for referral_data in pending_referrals:
                referral = Referral.from_dict(referral_data)

                # Complete the referral
                success = await self.complete_referral(referral.referral_id)
                if success:
                    validated_referral_ids.append(referral.referral_id)
                    logger.info(f"✅ Validated pending referral: {referral.referrer_id} -> {user_id}")
                else:
                    logger.error(f"❌ Failed to validate pending referral: {referral.referrer_id} -> {user_id}")

            return validated_referral_ids

        except Exception as e:
            logger.error(f"Failed to validate pending referrals for user {user_id}: {e}")
            return []

    async def get_pending_referrals_count(self, user_id: int) -> int:
        """Get count of pending referrals for a user"""
        try:
            count = await self.db.referrals.count_documents({
                'referrer_id': user_id,
                'status': ReferralStatus.PENDING.value
            })
            return count
        except Exception as e:
            logger.error(f"Failed to get pending referrals count for user {user_id}: {e}")
            return 0
    
    async def complete_referral(self, referral_id: str) -> bool:
        """Complete a referral and process reward"""
        try:
            # Get referral
            referral_data = await self.db.referrals.find_one({'referral_id': referral_id})
            if not referral_data:
                return False
            
            referral = Referral.from_dict(referral_data)
            
            if referral.status != ReferralStatus.PENDING:
                return False
            
            # Create transaction for reward
            transaction = Transaction(
                user_id=referral.referrer_id,
                amount=referral.reward_amount,
                transaction_type=TransactionType.REFERRAL_BONUS,
                status=TransactionStatus.COMPLETED,
                description=f"Referral bonus: {referral.reward_amount} Genesis Tokens for user {referral.referred_id}",
                reference_id=referral.referral_id
            )
            
            # Save transaction
            await self.db.transactions.insert_one(transaction.to_dict())
            
            # Update user balance
            await self.db.users.update_one(
                {'user_id': referral.referrer_id},
                {
                    '$inc': {
                        'balance': referral.reward_amount,
                        'total_earned': referral.reward_amount,
                        'successful_referrals': 1
                    },
                    '$set': {'updated_at': datetime.now(timezone.utc).isoformat()}
                }
            )
            
            # Complete referral
            referral.complete_referral(referral.reward_amount, transaction.transaction_id)
            referral.mark_rewarded(transaction.transaction_id)
            
            # Update referral in database
            await self.db.referrals.update_one(
                {'referral_id': referral_id},
                {'$set': referral.to_dict()}
            )
            
            log_transaction(
                referral.referrer_id,
                "REFERRAL_BONUS",
                referral.reward_amount,
                "COMPLETED",
                f"Referral ID: {referral_id}"
            )
            
            logger.info(f"Completed referral {referral_id}, rewarded {referral.reward_amount} Genesis Tokens")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete referral {referral_id}: {e}")
            return False
    
    async def get_user_referrals(self, user_id: int, limit: int = 50) -> List[Referral]:
        """Get referrals made by a user"""
        try:
            cursor = self.db.referrals.find({'referrer_id': user_id}).sort('created_at', -1).limit(limit)
            referrals = []
            
            async for referral_data in cursor:
                referrals.append(Referral.from_dict(referral_data))
            
            return referrals
            
        except Exception as e:
            logger.error(f"Failed to get referrals for user {user_id}: {e}")
            return []
    
    async def get_referral_count(self, user_id: int) -> int:
        """Get total referral count for a user"""
        try:
            return await self.db.referrals.count_documents({'referrer_id': user_id})
        except Exception as e:
            logger.error(f"Failed to get referral count for user {user_id}: {e}")
            return 0
    
    async def get_successful_referral_count(self, user_id: int) -> int:
        """Get successful referral count for a user"""
        try:
            return await self.db.referrals.count_documents({
                'referrer_id': user_id,
                'status': ReferralStatus.COMPLETED.value
            })
        except Exception as e:
            logger.error(f"Failed to get successful referral count for user {user_id}: {e}")
            return 0
    
    async def get_referral_earnings(self, user_id: int) -> float:
        """Get total earnings from referrals"""
        try:
            pipeline = [
                {'$match': {'referrer_id': user_id, 'is_rewarded': True}},
                {'$group': {'_id': None, 'total': {'$sum': '$reward_amount'}}}
            ]
            
            result = await self.db.referrals.aggregate(pipeline).to_list(1)
            
            if result:
                return result[0]['total']
            return 0.0
            
        except Exception as e:
            logger.error(f"Failed to get referral earnings for user {user_id}: {e}")
            return 0.0
    
    async def get_referral_leaderboard(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get referral leaderboard"""
        try:
            pipeline = [
                {'$match': {'status': ReferralStatus.COMPLETED.value}},
                {'$group': {
                    '_id': '$referrer_id',
                    'referral_count': {'$sum': 1},
                    'total_earned': {'$sum': '$reward_amount'}
                }},
                {'$sort': {'referral_count': -1}},
                {'$limit': limit}
            ]
            
            leaderboard = []
            async for entry in self.db.referrals.aggregate(pipeline):
                # Get user info
                user_data = await self.db.users.find_one({'user_id': entry['_id']})
                if user_data:
                    leaderboard.append({
                        'user_id': entry['_id'],
                        'username': user_data.get('username'),
                        'first_name': user_data.get('first_name'),
                        'referral_count': entry['referral_count'],
                        'total_earned': entry['total_earned']
                    })
            
            return leaderboard
            
        except Exception as e:
            logger.error(f"Failed to get referral leaderboard: {e}")
            return []
    
    async def get_referral_statistics(self) -> Dict[str, Any]:
        """Get overall referral statistics"""
        try:
            total_referrals = await self.db.referrals.count_documents({})
            completed_referrals = await self.db.referrals.count_documents({
                'status': ReferralStatus.COMPLETED.value
            })
            
            # Get total rewards paid
            pipeline = [
                {'$match': {'is_rewarded': True}},
                {'$group': {'_id': None, 'total': {'$sum': '$reward_amount'}}}
            ]
            
            result = await self.db.referrals.aggregate(pipeline).to_list(1)
            total_rewards = result[0]['total'] if result else 0.0
            
            # Get top referrer
            top_referrer_pipeline = [
                {'$match': {'status': ReferralStatus.COMPLETED.value}},
                {'$group': {
                    '_id': '$referrer_id',
                    'count': {'$sum': 1}
                }},
                {'$sort': {'count': -1}},
                {'$limit': 1}
            ]
            
            top_referrer_result = await self.db.referrals.aggregate(top_referrer_pipeline).to_list(1)
            top_referrer_id = top_referrer_result[0]['_id'] if top_referrer_result else None
            top_referrer_count = top_referrer_result[0]['count'] if top_referrer_result else 0
            
            return {
                'total_referrals': total_referrals,
                'completed_referrals': completed_referrals,
                'completion_rate': (completed_referrals / total_referrals * 100) if total_referrals > 0 else 0,
                'total_rewards_paid': total_rewards,
                'top_referrer_id': top_referrer_id,
                'top_referrer_count': top_referrer_count
            }
            
        except Exception as e:
            logger.error(f"Failed to get referral statistics: {e}")
            return {}
    
    async def invalidate_referral(self, referral_id: str, reason: str = "") -> bool:
        """Invalidate a referral"""
        try:
            referral_data = await self.db.referrals.find_one({'referral_id': referral_id})
            if not referral_data:
                return False
            
            referral = Referral.from_dict(referral_data)
            referral.invalidate_referral(reason)
            
            # Update in database
            await self.db.referrals.update_one(
                {'referral_id': referral_id},
                {'$set': referral.to_dict()}
            )
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate referral {referral_id}: {e}")
            return False
