"""
Logging configuration for the Telegram Referral Bot
"""

import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path
from config import Config

def setup_logger(name: str = None) -> logging.Logger:
    """Setup and configure logger"""
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Configure logger
    logger_name = name or __name__.split('.')[0]
    logger = logging.getLogger(logger_name)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Set log level
    log_level = getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        logs_dir / Config.LOG_FILE,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler - Only show warnings and errors for clean output
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)
    console_handler.setFormatter(simple_formatter)
    
    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        logs_dir / 'error.log',
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.addHandler(error_handler)

    # Reduce verbosity of third-party libraries
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('pymongo').setLevel(logging.WARNING)

    return logger


def setup_production_logging():
    """Setup production-ready logging with minimal console output"""
    # Set root logger to WARNING for console
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
            handler.setLevel(logging.WARNING)

    # Create a startup logger that shows essential messages
    startup_logger = logging.getLogger('startup')
    if not startup_logger.handlers:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(message)s', datefmt='%H:%M:%S')
        console_handler.setFormatter(formatter)
        startup_logger.addHandler(console_handler)
        startup_logger.setLevel(logging.INFO)
        startup_logger.propagate = False

    return startup_logger


def log_admin_action(admin_id: int, action: str, target: str = "", details: str = ""):
    """Log admin actions for audit trail"""
    logger = logging.getLogger('admin_actions')
    
    if not logger.handlers:
        # Setup admin actions logger
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        handler = logging.handlers.RotatingFileHandler(
            logs_dir / 'admin_actions.log',
            maxBytes=20*1024*1024,  # 20MB
            backupCount=10,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - ADMIN:%(admin_id)s - %(action)s - TARGET:%(target)s - %(details)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    logger.info("", extra={
        'admin_id': admin_id,
        'action': action,
        'target': target,
        'details': details
    })

def log_transaction(user_id: int, transaction_type: str, amount: float, status: str, details: str = ""):
    """Log financial transactions"""
    logger = logging.getLogger('transactions')
    
    if not logger.handlers:
        # Setup transactions logger
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        handler = logging.handlers.RotatingFileHandler(
            logs_dir / 'transactions.log',
            maxBytes=50*1024*1024,  # 50MB
            backupCount=20,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - USER:%(user_id)s - TYPE:%(transaction_type)s - AMOUNT:%(amount)s - STATUS:%(status)s - %(details)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    logger.info("", extra={
        'user_id': user_id,
        'transaction_type': transaction_type,
        'amount': f"💎{amount:.2f}",
        'status': status,
        'details': details
    })

def log_security_event(event_type: str, user_id: int = None, ip_address: str = "", details: str = ""):
    """Log security-related events"""
    logger = logging.getLogger('security')
    
    if not logger.handlers:
        # Setup security logger
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        handler = logging.handlers.RotatingFileHandler(
            logs_dir / 'security.log',
            maxBytes=20*1024*1024,  # 20MB
            backupCount=10,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - EVENT:%(event_type)s - USER:%(user_id)s - IP:%(ip_address)s - %(details)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.WARNING)
    
    logger.warning("", extra={
        'event_type': event_type,
        'user_id': user_id or 'N/A',
        'ip_address': ip_address or 'N/A',
        'details': details
    })
